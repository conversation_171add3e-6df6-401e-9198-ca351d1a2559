import ai.z.openapi.ZhipuAiClient;
import ai.z.openapi.service.web_search.WebSearchRequest;
import ai.z.openapi.service.web_search.WebSearchResponse;
import ai.z.openapi.service.web_search.WebSearchService;

public class Main {

    public static void main(String[] args) {

        ZhipuAiClient client = ZhipuAiClient.builder().build();;
        WebSearchService webSearchService = client.webSearch();

        WebSearchRequest request = WebSearchRequest.builder()
                .searchEngine("search_pro")
                .searchQuery("搜索2025年4月的财经新闻")
                .count(15)  // 返回结果的条数，范围1-50，默认10
                .searchDomainFilter("www.sohu.com")  // 只访问指定域名的内容
                .searchRecencyFilter("noLimit")  // 搜索指定日期范围内的内容
                .contentSize("high")  // 控制网页摘要的字数，默认medium
                .build();

        WebSearchResponse response = webSearchService.createWebSearch(request);
        System.out.println(response);

    }
}
